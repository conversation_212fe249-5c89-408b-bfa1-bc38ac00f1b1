import os
import time
import pyautogui as pg

POS = {
    'format_btn': (786, 728),
    'asc': (633, 805),
    'confirm': (470, 507),
    'save': (927, 674)
}

def click(pos, delay=0.3):
    pg.click(*pos)
    time.sleep(delay)

def convert_files(root_folder):
    # 遍历根目录下的所有子文件夹
    for foldername, subfolders, filenames in os.walk(root_folder):
        for filename in filenames:
            if not filename.endswith(".sor"):
                continue

            file_path = os.path.join(foldername, filename)
            os.startfile(file_path)
            time.sleep(0.5)

            try:
                pg.hotkey('ctrl', 's')
                time.sleep(0.5)

                for btn in ['format_btn', 'asc', 'confirm', 'save']:
                    click(POS[btn])

                print(f"{filename} 转换成功")

            except Exception as e:
                print(f"{filename} 转换失败: {str(e)}")

            finally:
                pg.hotkey('alt', 'f4')
                time.sleep(0.5)  # 增加关闭后的缓冲

# 传入大文件夹路径
convert_files(r"C:\Users\<USER>\Desktop\干线测试")