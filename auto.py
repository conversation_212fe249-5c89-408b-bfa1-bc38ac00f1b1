import os
import time
import pyautogui as pg

# 配置参数
POS = {
    'format_btn': (786, 728),
    'asc': (633, 805),
    'confirm': (470, 507),
    'save': (927, 674)
}

def get_mouse_position():
    """获取鼠标位置"""
    print("请将鼠标移动到目标位置，5秒后记录位置...")
    time.sleep(5)
    x, y = pg.position()
    print(f"当前鼠标位置: ({x}, {y})")

def click(pos, delay=0.5):
    print(f"点击位置: {pos}")  # 添加调试信息
    pg.click(*pos)
    time.sleep(delay)

def convert_files(folder):
    print("开始转换文件...")
    files = [f for f in os.listdir(folder) if f.endswith(".sor")]
    if not files:
        print(f"在文件夹 {folder} 中没有找到.sor文件")
        return

    print(f"找到 {len(files)} 个.sor文件")
    
    for file in files:
        file_path = os.path.join(folder, file)
        print(f"\n处理文件: {file}")
        os.startfile(file_path)
        time.sleep(0.5)  # 增加等待时间

        try:
            print("按下Ctrl+S...")
            pg.hotkey('ctrl', 's')
            time.sleep(1)  # 增加等待时间

            for btn in ['format_btn', 'asc', 'confirm', 'save']:
                print(f"点击 {btn}...")
                click(POS[btn], delay=0.5)  # 增加点击间隔

            print(f"{file} 转换成功")

        except Exception as e:
            print(f"{file} 转换失败: {str(e)}")

        finally:
            print("关闭窗口...")
            pg.hotkey('alt', 'f4')
            time.sleep(1)

if __name__ == "__main__":
    test_mouse = input("是否需要测试鼠标位置？(y/n): ")
    
    if test_mouse.lower() == 'y':
        # 获取鼠标位置的测试
        print("=== 鼠标位置测试 ===")
        print("按下Ctrl+C可以退出程序")
        while True:
            try:
                get_mouse_position()
            except KeyboardInterrupt:
                print("\n测试结束")
                break

    response = input("\n是否开始执行文件转换？(y/n): ")
    if response.lower() == 'y':
        convert_files(r"C:\Users\<USER>\Desktop\佛山联通\2024年第四季度干线测试\ShundeYongfeng_FoshanSanshan(105Guodao)_72F")