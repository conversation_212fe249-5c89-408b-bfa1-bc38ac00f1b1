#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
同缆组配置文件管理工具
支持JSON和CSV格式的配置文件创建、编辑和转换
"""

import os
import json
import pandas as pd
from typing import Dict, List, Optional

class CableConfigManager:
    """同缆组配置管理器"""
    
    def __init__(self):
        self.cable_groups: Dict[int, List[str]] = {}
    
    def load_from_json(self, config_file: str = "cable_groups.json") -> bool:
        """从JSON文件加载配置"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
                self.cable_groups = {int(k): v for k, v in config['cable_groups'].items()}
            print(f"成功从 {config_file} 加载了 {len(self.cable_groups)} 个同缆组")
            return True
        except FileNotFoundError:
            print(f"配置文件 {config_file} 未找到")
            return False
        except Exception as e:
            print(f"加载JSON配置文件失败: {e}")
            return False
    
    def load_from_csv(self, config_file: str = "cable_groups.csv") -> bool:
        """从CSV文件加载配置"""
        try:
            df = pd.read_csv(config_file)
            self.cable_groups = {}
            for _, row in df.iterrows():
                group_id = int(row['group_id'])
                filename = row['filename']
                if group_id not in self.cable_groups:
                    self.cable_groups[group_id] = []
                self.cable_groups[group_id].append(filename)
            print(f"成功从 {config_file} 加载了 {len(self.cable_groups)} 个同缆组")
            return True
        except FileNotFoundError:
            print(f"配置文件 {config_file} 未找到")
            return False
        except Exception as e:
            print(f"加载CSV配置文件失败: {e}")
            return False
    
    def save_to_json(self, config_file: str = "cable_groups.json") -> bool:
        """保存到JSON文件"""
        try:
            config = {
                "cable_groups": {str(k): v for k, v in self.cable_groups.items()}
            }
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            print(f"配置已保存到 {config_file}")
            return True
        except Exception as e:
            print(f"保存JSON配置文件失败: {e}")
            return False
    
    def save_to_csv(self, config_file: str = "cable_groups.csv") -> bool:
        """保存到CSV文件"""
        try:
            data = []
            for group_id, filenames in self.cable_groups.items():
                for filename in filenames:
                    data.append({'group_id': group_id, 'filename': filename})
            df = pd.DataFrame(data)
            df.to_csv(config_file, index=False)
            print(f"配置已保存到 {config_file}")
            return True
        except Exception as e:
            print(f"保存CSV配置文件失败: {e}")
            return False
    
    def add_file_to_group(self, group_id: int, filename: str) -> None:
        """将文件添加到指定组"""
        if group_id not in self.cable_groups:
            self.cable_groups[group_id] = []
        if filename not in self.cable_groups[group_id]:
            self.cable_groups[group_id].append(filename)
            print(f"已将 {filename} 添加到组 {group_id}")
        else:
            print(f"文件 {filename} 已存在于组 {group_id}")
    
    def remove_file_from_group(self, group_id: int, filename: str) -> bool:
        """从指定组移除文件"""
        if group_id in self.cable_groups and filename in self.cable_groups[group_id]:
            self.cable_groups[group_id].remove(filename)
            # 如果组变空，删除该组
            if not self.cable_groups[group_id]:
                del self.cable_groups[group_id]
            print(f"已从组 {group_id} 移除 {filename}")
            return True
        else:
            print(f"文件 {filename} 不在组 {group_id} 中")
            return False
    
    def move_file_to_group(self, filename: str, from_group: int, to_group: int) -> bool:
        """将文件从一个组移动到另一个组"""
        if self.remove_file_from_group(from_group, filename):
            self.add_file_to_group(to_group, filename)
            return True
        return False
    
    def get_file_group(self, filename: str) -> Optional[int]:
        """获取文件所属的组"""
        for group_id, files in self.cable_groups.items():
            if filename in files:
                return group_id
        return None
    
    def list_groups(self) -> None:
        """列出所有组及其文件"""
        if not self.cable_groups:
            print("当前没有配置任何同缆组")
            return
        
        print("当前同缆组配置:")
        for group_id in sorted(self.cable_groups.keys()):
            files = self.cable_groups[group_id]
            print(f"\n组 {group_id} ({len(files)} 个文件):")
            for i, filename in enumerate(files, 1):
                print(f"  {i:2d}. {filename}")
    
    def create_new_group(self, filenames: List[str]) -> int:
        """创建新组"""
        # 找到下一个可用的组ID
        if self.cable_groups:
            new_group_id = max(self.cable_groups.keys()) + 1
        else:
            new_group_id = 1
        
        self.cable_groups[new_group_id] = filenames.copy()
        print(f"创建了新组 {new_group_id}，包含 {len(filenames)} 个文件")
        return new_group_id
    
    def delete_group(self, group_id: int) -> bool:
        """删除指定组"""
        if group_id in self.cable_groups:
            del self.cable_groups[group_id]
            print(f"已删除组 {group_id}")
            return True
        else:
            print(f"组 {group_id} 不存在")
            return False
    
    def convert_json_to_csv(self, json_file: str = "cable_groups.json", 
                           csv_file: str = "cable_groups.csv") -> bool:
        """将JSON配置转换为CSV格式"""
        if self.load_from_json(json_file):
            return self.save_to_csv(csv_file)
        return False
    
    def convert_csv_to_json(self, csv_file: str = "cable_groups.csv", 
                           json_file: str = "cable_groups.json") -> bool:
        """将CSV配置转换为JSON格式"""
        if self.load_from_csv(csv_file):
            return self.save_to_json(json_file)
        return False

def interactive_config_manager():
    """交互式配置管理器"""
    manager = CableConfigManager()
    
    # 尝试加载现有配置
    if os.path.exists("cable_groups.json"):
        manager.load_from_json()
    elif os.path.exists("cable_groups.csv"):
        manager.load_from_csv()
    
    while True:
        print("\n" + "="*50)
        print("同缆组配置管理器")
        print("="*50)
        print("1. 查看所有组")
        print("2. 添加文件到组")
        print("3. 从组中移除文件")
        print("4. 移动文件到其他组")
        print("5. 创建新组")
        print("6. 删除组")
        print("7. 保存为JSON格式")
        print("8. 保存为CSV格式")
        print("9. 格式转换")
        print("0. 退出")
        
        choice = input("\n请选择操作 (0-9): ").strip()
        
        if choice == "0":
            print("退出程序")
            break
        elif choice == "1":
            manager.list_groups()
        elif choice == "2":
            group_id = int(input("请输入组ID: "))
            filename = input("请输入文件名: ").strip()
            manager.add_file_to_group(group_id, filename)
        elif choice == "3":
            group_id = int(input("请输入组ID: "))
            filename = input("请输入文件名: ").strip()
            manager.remove_file_from_group(group_id, filename)
        elif choice == "4":
            filename = input("请输入文件名: ").strip()
            from_group = int(input("请输入源组ID: "))
            to_group = int(input("请输入目标组ID: "))
            manager.move_file_to_group(filename, from_group, to_group)
        elif choice == "5":
            filenames = input("请输入文件名(用逗号分隔): ").strip().split(',')
            filenames = [f.strip() for f in filenames if f.strip()]
            manager.create_new_group(filenames)
        elif choice == "6":
            group_id = int(input("请输入要删除的组ID: "))
            manager.delete_group(group_id)
        elif choice == "7":
            filename = input("请输入JSON文件名 (默认: cable_groups.json): ").strip()
            if not filename:
                filename = "cable_groups.json"
            manager.save_to_json(filename)
        elif choice == "8":
            filename = input("请输入CSV文件名 (默认: cable_groups.csv): ").strip()
            if not filename:
                filename = "cable_groups.csv"
            manager.save_to_csv(filename)
        elif choice == "9":
            print("1. JSON转CSV")
            print("2. CSV转JSON")
            convert_choice = input("请选择转换方向: ").strip()
            if convert_choice == "1":
                manager.convert_json_to_csv()
            elif convert_choice == "2":
                manager.convert_csv_to_json()
        else:
            print("无效选择，请重新输入")

if __name__ == "__main__":
    interactive_config_manager()
