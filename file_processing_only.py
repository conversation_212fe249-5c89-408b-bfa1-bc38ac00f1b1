import os
import re
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt

# 设置支持中文的字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'Microsoft YaHei', 'WenQuanYi Micro Hei']
plt.rcParams['axes.unicode_minus'] = False

# 设置已知的同缆组
CABLE_GROUPS = {
    1: ['ShundeYongfeng_DianxinNanqu_48F_01.asc', 'ShundeYongfeng_DianxinNanqu_48F_02.asc', 
        'ShundeYongfeng_DianxinNanqu_48F_03.asc', 'ShundeYongfeng_DianxinNanqu_48F_04.asc', 
        'ShundeYongfeng_DianxinNanqu_48F_05.asc', 'ShundeYongfeng_DianxinNanqu_48F_06.asc', 
        'ShundeYongfeng_DianxinNanqu_48F_07.asc', '<PERSON>ndeYongfeng_DianxinNanqu_48F_08.asc', 
        'ShundeYongfeng_DianxinNanqu_48F_09.asc', 'ShundeYongfeng_DianxinNanqu_48F_10.asc'],
    2: ['ShundeYongfeng_DianxinNanqu_48F_11.asc', 'ShundeYongfeng_DianxinNanqu_48F_12.asc', 
        'ShundeYongfeng_DianxinNanqu_48F_23.asc', 'ShundeYongfeng_DianxinNanqu_48F_24.asc'],
    3: ['ShundeYongfeng_DianxinNanqu_48F_13.asc', 'ShundeYongfeng_DianxinNanqu_48F_14.asc'],
    4: ['ShundeYongfeng_DianxinNanqu_48F_18.asc', 'ShundeYongfeng_DianxinNanqu_48F_21.asc', 
        'ShundeYongfeng_DianxinNanqu_48F_22.asc'],
    5: ['ShundeYongfeng_DianxinNanqu_48F_25.asc', 'ShundeYongfeng_DianxinNanqu_48F_26.asc', 
        'ShundeYongfeng_DianxinNanqu_48F_27.asc', 'ShundeYongfeng_DianxinNanqu_48F_28.asc', 
        'ShundeYongfeng_DianxinNanqu_48F_29.asc', 'ShundeYongfeng_DianxinNanqu_48F_30.asc', 
        'ShundeYongfeng_DianxinNanqu_48F_31.asc', 'ShundeYongfeng_DianxinNanqu_48F_32.asc', 
        'ShundeYongfeng_DianxinNanqu_48F_33.asc', 'ShundeYongfeng_DianxinNanqu_48F_34.asc', 
        'ShundeYongfeng_DianxinNanqu_48F_35.asc', 'ShundeYongfeng_DianxinNanqu_48F_36.asc', 
        'ShundeYongfeng_DianxinNanqu_48F_37.asc', 'ShundeYongfeng_DianxinNanqu_48F_38.asc', 
        'ShundeYongfeng_DianxinNanqu_48F_39.asc', 'ShundeYongfeng_DianxinNanqu_48F_40.asc', 
        'ShundeYongfeng_DianxinNanqu_48F_42.asc', 'ShundeYongfeng_DianxinNanqu_48F_45.asc', 
        'ShundeYongfeng_DianxinNanqu_48F_47.asc', 'ShundeYongfeng_DianxinNanqu_48F_48.asc'],
    6: ['ShundeYongfeng_DianxinNanqu_48F_41.asc', 'ShundeYongfeng_DianxinNanqu_48F_43.asc', 
        'ShundeYongfeng_DianxinNanqu_48F_44.asc'],
    7: ['ShundeYongfeng_DianxinNanqu_48F_46.asc'],
    8: ['ShundeYongfeng_GuangzhouQisuo_48F_01.asc', 'ShundeYongfeng_GuangzhouQisuo_48F_04.asc',
         'ShundeYongfeng_GuangzhouQisuo_48F_06.asc', 'ShundeYongfeng_GuangzhouQisuo_48F_09.asc',
         'ShundeYongfeng_GuangzhouQisuo_48F_11.asc', 'ShundeYongfeng_GuangzhouQisuo_48F_14.asc',
         'ShundeYongfeng_GuangzhouQisuo_48F_19.asc', 'ShundeYongfeng_GuangzhouQisuo_48F_20.asc',
         'ShundeYongfeng_GuangzhouQisuo_48F_21.asc', 'ShundeYongfeng_GuangzhouQisuo_48F_23.asc',
         'ShundeYongfeng_GuangzhouQisuo_48F_24.asc', 'ShundeYongfeng_GuangzhouQisuo_48F_25.asc',
         'ShundeYongfeng_GuangzhouQisuo_48F_26.asc', 'ShundeYongfeng_GuangzhouQisuo_48F_28.asc',
         'ShundeYongfeng_GuangzhouQisuo_48F_29.asc', 'ShundeYongfeng_GuangzhouQisuo_48F_30.asc',
         'ShundeYongfeng_GuangzhouQisuo_48F_31.asc', 'ShundeYongfeng_GuangzhouQisuo_48F_32.asc',
         'ShundeYongfeng_GuangzhouQisuo_48F_35.asc', 'ShundeYongfeng_GuangzhouQisuo_48F_36.asc',
         'ShundeYongfeng_GuangzhouQisuo_48F_37.asc', 'ShundeYongfeng_GuangzhouQisuo_48F_38.asc',
         'ShundeYongfeng_GuangzhouQisuo_48F_40.asc', 'ShundeYongfeng_GuangzhouQisuo_48F_41.asc',
         'ShundeYongfeng_GuangzhouQisuo_48F_42.asc', 'ShundeYongfeng_GuangzhouQisuo_48F_43.asc',
         'ShundeYongfeng_GuangzhouQisuo_48F_44.asc'],
    9: ['ShundeYongfeng_GuangzhouQisuo_48F_02.asc', 'ShundeYongfeng_GuangzhouQisuo_48F_39.asc'],
    10: ['ShundeYongfeng_GuangzhouQisuo_48F_13.asc', 'ShundeYongfeng_GuangzhouQisuo_48F_33.asc'],
    11: ['ShundeYongfeng_GuangzhouQisuo_48F_45.asc', 'ShundeYongfeng_GuangzhouQisuo_48F_46.asc'],
    12: ['ShundeYongfeng_GuangzhouQisuo_48F_03.asc'],
    13: ['ShundeYongfeng_GuangzhouQisuo_48F_18.asc'],
    14: ['ShundeYongfeng_FoshanChaoan_48F_23.asc'],
    15: ['ShundeYongfeng_FoshanChaoan_48F_30.asc'],
    16: ['ShundeYongfeng_FoshanChaoan_48F_01.asc', 'ShundeYongfeng_FoshanChaoan_48F_02.asc',
         'ShundeYongfeng_FoshanChaoan_48F_03.asc', 'ShundeYongfeng_FoshanChaoan_48F_05.asc',
         'ShundeYongfeng_FoshanChaoan_48F_07.asc', 'ShundeYongfeng_FoshanChaoan_48F_10.asc',
         'ShundeYongfeng_FoshanChaoan_48F_12.asc', 'ShundeYongfeng_FoshanChaoan_48F_13.asc',
         'ShundeYongfeng_FoshanChaoan_48F_14.asc', 'ShundeYongfeng_FoshanChaoan_48F_15.asc',
         'ShundeYongfeng_FoshanChaoan_48F_24.asc', 'ShundeYongfeng_FoshanChaoan_48F_31.asc',
         'ShundeYongfeng_FoshanChaoan_48F_33.asc', 'ShundeYongfeng_FoshanChaoan_48F_35.asc',
         'ShundeYongfeng_FoshanChaoan_48F_48.asc'],
    17: ['ShundeYongfeng_FoshanChaoan_48F_04.asc', 'ShundeYongfeng_FoshanChaoan_48F_17.asc',
         'ShundeYongfeng_FoshanChaoan_48F_22.asc', 'ShundeYongfeng_FoshanChaoan_48F_29.asc',
         'ShundeYongfeng_FoshanChaoan_48F_45.asc'],
    18: ['ShundeYongfeng_FoshanChaoan_48F_28.asc', 'ShundeYongfeng_FoshanChaoan_48F_32.asc']
}

def get_cable_group(filename):
    """获取文件所属的同缆组"""
    for group_id, files in CABLE_GROUPS.items():
        if filename in files:
            return group_id
    return -1  # 返回-1表示不属于任何已知组

def process_otdr_file(file_path):
    """处理单个OTDR文件,去除首尾异常点"""
    with open(file_path, 'r') as f:
        content = f.read()

    # 提取数据点（跳过非数字行）
    data_lines = []
    in_data_section = False
    for line in content.split('\n'):
        if "POS(km)" in line:
            in_data_section = True
            continue
        if in_data_section and line.strip():
            if re.match(r"^\d+\.\d+\s+-?\d+\.\d+", line):
                data_lines.append(line)

    # 转换为数值
    data = []
    for line in data_lines:
        pos, val = map(float, line.strip().split())
        data.append([pos, val])

    data = np.array(data)

    # 去除首端前10个点
    if len(data) > 10:
        data = data[10:]

    # 去除尾端50个点
    if len(data) > 50:
        data = data[:-50]

    return data

def process_folder(folder_path):
    """处理整个文件夹及其子文件夹的OTDR文件
    
    Args:
        folder_path: 根文件夹路径
        
    Returns:
        dict: 以文件名为键，数据为值的字典
    """
    file_dict = {}
    processed_count = 0
    
    def process_single_file(file_path, prefix=""):
        """处理单个文件并返回数据"""
        try:
            data = process_otdr_file(file_path)
            if len(data) > 0:
                return data
        except Exception as e:
            print(f"处理文件失败: {os.path.basename(file_path)}")
        return None
    
    def walk_folder(current_path, parent_prefix=""):
        """递归遍历文件夹"""
        nonlocal processed_count
        for item in os.listdir(current_path):
            item_path = os.path.join(current_path, item)
            
            relative_path = os.path.relpath(os.path.dirname(item_path), folder_path)
            current_prefix = relative_path.replace("\\", "_").replace("/", "_")
            if current_prefix == ".":
                current_prefix = ""
            
            if os.path.isfile(item_path) and item.endswith(".asc"):
                data = process_single_file(item_path)
                if data is not None:
                    new_filename = f"{current_prefix}_{item}" if current_prefix else item
                    file_dict[new_filename] = data
                    processed_count += 1
            
            elif os.path.isdir(item_path):
                walk_folder(item_path)
    
    print(f"开始处理文件夹: {folder_path}")
    walk_folder(folder_path)
    print(f"成功处理 {processed_count} 个OTDR文件")
    
    return file_dict

def plot_all_curves(file_dict):
    """绘制所有曲线在同一图中"""
    plt.figure(figsize=(12, 6))

    # 直接绘制所有曲线
    for filename, data in file_dict.items():
        plt.plot(data[:, 0], data[:, 1],
                linewidth=1.5,
                alpha=0.7,
                label=filename)

    plt.title("OTDR曲线综合比对", pad=20)
    plt.xlabel("Position (km)")
    plt.ylabel("Value (dB)")
    plt.grid(True, alpha=0.3)
    plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    plt.tight_layout()
    plt.show()


# 主函数
if __name__ == "__main__":
    print("\n=== 开始文件处理程序 ===")
    
    folder_path = "C:/Users/<USER>/Desktop/干线测试" 
    file_data = process_folder(folder_path)
    
    if not file_data:
        print("未找到有效的OTDR文件！")
    else:
        print(f"成功读取 {len(file_data)} 个OTDR文件")
        # 绘制所有曲线
        plot_all_curves(file_data)