import os
import re
import numpy as np
import pandas as pd
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
import matplotlib.pyplot as plt
from itertools import combinations
from sklearn.metrics import confusion_matrix, classification_report, roc_curve, auc
import seaborn as sns

# 设置支持中文的字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'Microsoft YaHei', 'WenQuanYi Micro Hei']
plt.rcParams['axes.unicode_minus'] = False

# 定义已知的同缆组
CABLE_GROUPS = {
    1: ['01.asc', '02.asc', '03.asc', '04.asc', '05.asc', '06.asc', '07.asc', 
        '08.asc', '09.asc', '10.asc'],
    2: ['11.asc', '12.asc', '23.asc', '24.asc'],
    3: ['13.asc', '14.asc'],
    4: ['18.asc', '21.asc', '22.asc'],
    5: ['25.asc', '26.asc', '27.asc', '28.asc', '29.asc', '30.asc', '31.asc', 
        '32.asc', '33.asc', '34.asc', '35.asc', '36.asc', '37.asc', '38.asc', 
        '39.asc', '40.asc', '42.asc','45.asc', '47.asc', '48.asc'],
    6: ['41.asc', '43.asc', '44.asc'],
}

def get_cable_group(filename):
    """获取文件所属的同缆组"""
    for group_id, files in CABLE_GROUPS.items():
        if filename in files:
            return group_id
    return -1  # 返回-1表示不属于任何已知组

def process_otdr_file(file_path):
    """处理单个OTDR文件，去除首尾异常点"""
    with open(file_path, 'r') as f:
        content = f.read()

    # 提取数据点（跳过非数字行）
    data_lines = []
    in_data_section = False
    for line in content.split('\n'):
        if "POS(km)" in line:
            in_data_section = True
            continue
        if in_data_section and line.strip():
            if re.match(r"^\d+\.\d+\s+-?\d+\.\d+", line):
                data_lines.append(line)

    # 转换为数值
    data = []
    for line in data_lines:
        pos, val = map(float, line.strip().split())
        data.append([pos, val])

    data = np.array(data)

    # 去除首端前10个点
    if len(data) > 10:
        data = data[10:]

    # 去除尾端50个点
    if len(data) > 50:
        data = data[:-50]

    return data

def extract_features(curve_data):
    """从OTDR曲线中提取特征"""
    # 获取位置和值
    positions = curve_data[:, 0]
    values = curve_data[:, 1]
    features = []
    
    # 1. 基本统计特征
    features.extend([
        np.mean(values),          # 均值
        np.std(values),           # 标准差
        np.max(values),           # 最大值
        np.min(values),           # 最小值
        np.max(values) - np.min(values),  # 范围
        curve_data[-1, 0],        # 光纤长度
        np.median(values),        # 中位数
        pd.Series(values).skew(), # 偏度
        pd.Series(values).kurtosis() # 峰度
    ])
    
    # 2. 一阶差分特征
    diff = np.diff(values)
    features.extend([
        np.mean(diff),
        np.std(diff),
        np.max(diff),
        np.min(diff),
        np.percentile(diff, 25),  # 25分位数
        np.percentile(diff, 75)   # 75分位数
    ])
    
    # 3. 二阶差分特征
    diff2 = np.diff(diff)
    features.extend([
        np.mean(diff2),
        np.std(diff2),
        np.max(diff2),
        np.min(diff2)
    ])
    
    # 4. 分段特征
    n_segments = 10
    segment_length = len(values) // n_segments
    for i in range(n_segments):
        start = i * segment_length
        end = (i + 1) * segment_length
        segment = values[start:end]
        features.extend([
            np.mean(segment),
            np.std(segment)
        ])
    
    # 5. 突变点特征
    threshold = np.std(diff) * 2
    mutation_points = np.where(np.abs(diff) > threshold)[0]
    features.extend([
        len(mutation_points),  # 突变点数量
        np.mean(np.abs(diff[mutation_points])) if len(mutation_points) > 0 else 0  # 平均突变强度
    ])
    
    return np.array(features)

def calculate_similarity(features1, features2):
    """计算两组特征的相似度"""
    # 1. 计算欧氏距离
    euclidean_dist = np.sqrt(np.sum((features1 - features2) ** 2))
    
    # 2. 计算余弦相似度
    cos_sim = np.dot(features1, features2) / (np.linalg.norm(features1) * np.linalg.norm(features2))
    
    # 3. 计算相对差异
    rel_diff = np.mean(np.abs(features1 - features2) / (np.abs(features1) + np.abs(features2) + 1e-10))
    
    return np.array([euclidean_dist, cos_sim, rel_diff])

def prepare_pair_features(file_dict):
    """准备曲线对的特征数据"""
    pairs_features = []
    pair_labels = []
    pair_names = []
    
    for (name1, data1), (name2, data2) in combinations(file_dict.items(), 2):
        # 提取每条曲线的特征
        features1 = extract_features(data1)
        features2 = extract_features(data2)
        
        # 计算相似度特征
        similarity_features = calculate_similarity(features1, features2)
        
        # 判断是否属于同一根光缆
        group1 = get_cable_group(name1)
        group2 = get_cable_group(name2)
        is_same_cable = int(group1 != -1 and group1 == group2)
        
        pairs_features.append(similarity_features)
        pair_labels.append(is_same_cable)
        pair_names.append((name1, name2))
    
    return np.array(pairs_features), np.array(pair_labels), pair_names

class TwoLevelRandomForest:
    def __init__(self):
        self.scaler = StandardScaler()
        # 增加决策树的数量以减少平票的可能性
        self.rf_level1 = RandomForestClassifier(n_estimators=201, random_state=42)  # 使用奇数个决策树
        self.rf_level2 = RandomForestClassifier(n_estimators=201, random_state=42)  # 使用奇数个决策树
        
    def fit(self, X, y):    
        # 第一层随机森林
        X_scaled = self.scaler.fit_transform(X) # 标准化处理
        self.rf_level1.fit(X_scaled, y) 
        
        # 获取第一层的预测概率作为第二层的输入特征
        level1_probs = self.rf_level1.predict_proba(X_scaled)
        
        # 将原始特征和第一层预测概率组合
        X_level2 = np.hstack([X_scaled, level1_probs])
        
        # 训练第二层随机森林
        self.rf_level2.fit(X_level2, y)
        
    def predict(self, X):
        X_scaled = self.scaler.transform(X)
        level1_probs = self.rf_level1.predict_proba(X_scaled)
        X_level2 = np.hstack([X_scaled, level1_probs])
        return self.rf_level2.predict(X_level2)
    
    def save_comparison_to_file(self, file1, file2, vote_count_same, total_trees, final_prediction):
        """将对比结果保存到文件"""
        with open('comparison_results.txt', 'a', encoding='utf-8') as f:
            f.write(f"\n对比文件: {file1} 和 {file2}\n")
            f.write("=" * 50 + "\n")
            f.write(f"投票统计:\n")
            f.write(f"同缆票数: {vote_count_same}\n")
            f.write(f"不同缆票数: {total_trees - vote_count_same}\n")
            f.write(f"总票数: {total_trees}\n")
            f.write(f"同缆比例: {vote_count_same/total_trees:.2%}\n")
            f.write(f"最终预测结果: {'同缆' if final_prediction == 1 else '不同缆'}\n")
            f.write("=" * 50 + "\n")

    def predict_with_voting(self, X, file1=None, file2=None):
        """使用投票机制进行预测，将详细对比结果保存到文件"""
        try:
            X_scaled = self.scaler.transform(X)
            
            # 获取第一层每棵树的预测
            trees_predictions_1 = []
            trees_probs_1 = []
            for tree in self.rf_level1.estimators_:
                pred = tree.predict(X_scaled)
                prob = tree.predict_proba(X_scaled)
                trees_predictions_1.append(pred)
                trees_probs_1.append(prob)
            
            # 计算第一层的平均概率
            level1_probs = np.mean(trees_probs_1, axis=0)
            X_level2 = np.hstack([X_scaled, level1_probs])
            
            # 获取第二层每棵树的预测
            vote_count_same = 0
            for tree in self.rf_level2.estimators_:
                pred = tree.predict(X_level2)
                if pred[0] == 1:
                    vote_count_same += 1
            
            # 统计总体投票结果
            total_trees = len(self.rf_level2.estimators_)
            
            # 多数投票决定
            final_prediction = 1 if vote_count_same > total_trees // 2 else 0
            
            # 如果提供了文件名，保存详细对比结果到文件
            if file1 and file2:
                self.save_comparison_to_file(file1, file2, vote_count_same, total_trees, final_prediction)
            
            return final_prediction
            
        except Exception as e:
            print(f"预测过程中出现错误: {str(e)}")
            return 0

def plot_all_curves(file_dict):
    """绘制所有曲线在同一图中"""
    plt.figure(figsize=(12, 6))
    colors = ['blue', 'green', 'red', 'purple', 'orange', 'brown']
    
    # 按同缆组对文件进行分组
    grouped_files = {}
    for filename, data in file_dict.items():
        group_id = get_cable_group(filename)
        if group_id not in grouped_files:
            grouped_files[group_id] = []
        grouped_files[group_id].append((filename, data))
    
    # 为每个组使用相同的颜色
    for group_id, files in grouped_files.items():
        color = colors[group_id % len(colors)] if group_id != -1 else 'gray'
        for filename, data in files:
            plt.plot(data[:, 0], data[:, 1],
                    color=color,
                    linewidth=1.5,
                    alpha=0.7,
                    label=f'{filename} (组{group_id if group_id != -1 else "未知"})')
    
    plt.title("OTDR曲线综合比对", pad=20)
    plt.xlabel("Position (km)")
    plt.ylabel("Value (dB)")
    plt.grid(True, alpha=0.3)
    plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    plt.tight_layout()
    plt.show()

def process_folder(folder_path):
    """处理整个文件夹的OTDR文件"""
    file_dict = {}
    for filename in os.listdir(folder_path):
        if filename.endswith(".asc"):
            file_path = os.path.join(folder_path, filename)
            try:
                data = process_otdr_file(file_path)
                if len(data) > 0:
                    file_dict[filename] = data
            except Exception as e:
                print(f"处理文件 {filename} 时出错: {str(e)}")
    return file_dict

def find_cable_groups(file_dict, model):
    """根据模型投票结果找出同缆组"""
    all_files = list(file_dict.keys())
    groups = []
    used_files = set()

    for start_file in all_files:
        if start_file in used_files:
            continue

        current_group = {start_file}
        used_files.add(start_file)
        
        # 检查与其他所有文件的关系
        similar_files = []
        for other_file in all_files:
            if other_file == start_file or other_file in used_files:
                continue

            # 提取特征
            data1 = file_dict[start_file]
            data2 = file_dict[other_file]
            features1 = extract_features(data1)
            features2 = extract_features(data2)
            similarity_features = calculate_similarity(features1, features2)

            # 使用投票预测是否同缆
            is_same_cable = model.predict_with_voting([similarity_features], start_file, other_file)
            if is_same_cable:
                similar_files.append(other_file)
        
        # 添加到当前组
        for other_file in similar_files:
            if other_file not in used_files:
                current_group.add(other_file)
                used_files.add(other_file)

        if len(current_group) > 1:  # 只保存有多个文件的组
            groups.append(sorted(list(current_group)))

    return groups

def predict_new_file(file_dict, model, new_filename):
    """预测新文件属于哪个同缆组"""
    if new_filename not in file_dict:
        print(f"错误：找不到文件 {new_filename}")
        return
    
    new_data = file_dict[new_filename]
    new_features = extract_features(new_data)
    
    # 与所有其他文件比较
    similar_files = []
    print(f"\n新文件 {new_filename} 的预测结果：")
    print("=" * 65)
    
    for other_filename, other_data in file_dict.items():
        if other_filename == new_filename:
            continue
            
        other_features = extract_features(other_data)
        similarity_features = calculate_similarity(new_features, other_features)
        
        # 使用投票预测是否同缆
        is_same_cable = model.predict_with_voting([similarity_features], new_filename, other_filename)
        if is_same_cable:
            true_group = get_cable_group(other_filename)
            similar_files.append((other_filename, true_group))
    
    if similar_files:
        print(f"发现 {len(similar_files)} 个同缆文件：")
        for filename, true_group in similar_files:
            print(f"  - {filename} (实际组别: {true_group if true_group != -1 else '未知'})")
        
        # 统计最可能的组别（使用简单计数）
        group_votes = {}
        for filename, true_group in similar_files:
            if true_group != -1:
                group_votes[true_group] = group_votes.get(true_group, 0) + 1
        
        if group_votes:
            most_likely_group = max(group_votes.items(), key=lambda x: x[1])[0]
            print(f"\n根据投票结果，{new_filename} 最可能属于第 {most_likely_group} 组")
    else:
        print("未找到同缆文件")
    
    print("=" * 65)

def analyze_prediction_errors(X_test, y_test, y_pred, pair_names_test, model):
    """分析预测错误的案例"""
    print("\n预测错误分析：")
    print("=" * 65)
    
    # 找出预测错误的索引
    error_indices = np.where(y_test != y_pred)[0]
    
    if len(error_indices) == 0:
        print("测试集中没有预测错误的案例！")
        return
    
    print(f"在测试集中发现 {len(error_indices)} 个预测错误：")
    
    for idx in error_indices:
        file1, file2 = pair_names_test[idx]
        true_label = y_test[idx]
        pred_label = y_pred[idx]
        
        print(f"\n错误案例 {idx + 1}:")
        print(f"文件对: {file1} 和 {file2}")
        print(f"真实情况: {'同缆' if true_label == 1 else '不同缆'}")
        print(f"预测结果: {'同缆' if pred_label == 1 else '不同缆'}")
        
        # 显示两个文件的实际组别
        group1 = get_cable_group(file1)
        group2 = get_cable_group(file2)
        print(f"实际组别: {file1} 属于组 {group1 if group1 != -1 else '未知'}, "
              f"{file2} 属于组 {group2 if group2 != -1 else '未知'}")
        print("-" * 40)

def evaluate_test_set(X_test, y_test, model, pair_names_test):
    """评估模型在测试集上的表现"""
    # 获取预测结果
    y_pred = model.predict(X_test)
    
    # 计算基本指标
    accuracy = np.mean(y_pred == y_test)
    
    print("\n测试集评估结果:")
    print("=" * 65)
    print(f"测试样本数量: {len(y_test)}")
    print(f"准确率 (Accuracy): {accuracy:.4f} ({accuracy:.2%})")
    
    # 创建并显示混淆矩阵
    cm = confusion_matrix(y_test, y_pred)
    print("\n混淆矩阵:")
    print("-" * 40)
    print(f"真正例 (TP): {cm[1][1]}") # 预测为同缆，实际为同缆
    print(f"假正例 (FP): {cm[0][1]}") # 预测为同缆，实际为不同缆
    print(f"真负例 (TN): {cm[0][0]}") # 预测为不同缆，实际为不同缆
    print(f"假负例 (FN): {cm[1][0]}") # 预测为不同缆，实际为同缆
    
    # 计算F1分数
    precision = cm[1][1] / (cm[1][1] + cm[0][1]) if (cm[1][1] + cm[0][1]) > 0 else 0
    recall = cm[1][1] / (cm[1][1] + cm[1][0]) if (cm[1][1] + cm[1][0]) > 0 else 0
    f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
    
    print("\n关键评估指标:")
    print("-" * 40)
    print(f"F1分数: {f1:.4f} - 模型整体性能的综合评价")
    
    # 绘制混淆矩阵热图
    plt.figure(figsize=(8, 6))
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
                xticklabels=["不同缆", "同缆"], 
                yticklabels=["不同缆", "同缆"])
    plt.ylabel('真实标签')
    plt.xlabel('预测标签')
    plt.title('混淆矩阵')
    plt.show()
    
    return accuracy, f1

if __name__ == "__main__":
    # 清空之前的比较结果文件
    with open('comparison_results.txt', 'w', encoding='utf-8') as f:
        f.write("OTDR曲线对比结果\n")
        f.write("=" * 50 + "\n")
    
    folder_path = "C:/Users/<USER>/Desktop/干线测试/ShundeYongfeng_DianxinNanqu_48F" 
    file_data = process_folder(folder_path)
    
    if not file_data:
        print("未找到有效的OTDR文件！")
    else:
        print(f"成功读取 {len(file_data)} 个OTDR文件")
        
        # 显示所有曲线（按组着色）
        plot_all_curves(file_data)
        
        try:
            # 准备特征数据
            X, y, pair_names = prepare_pair_features(file_data)
            
            # 划分训练集和测试集
            X_train, X_test, y_train, y_test, pair_names_train, pair_names_test = train_test_split(
                X, y, pair_names, test_size=0.3, random_state=42)
            
            # 训练双层随机森林模型
            model = TwoLevelRandomForest()
            model.fit(X_train, y_train)
            
            # 详细评估测试集表现
            evaluate_test_set(X_test, y_test, model, pair_names_test)
            
            # 获取预测结果，用于错误分析
            y_pred = model.predict(X_test)
            
            # 分析预测错误
            analyze_prediction_errors(X_test, y_test, y_pred, pair_names_test, model)
            
            # 找出模型预测的同缆组
            predicted_groups = find_cable_groups(file_data, model)
            
            print("\n最终分组结果：")
            print("=" * 65)
            for i, group in enumerate(predicted_groups, 1):
                print(f"\n第 {i} 组同缆文件:")
                for filename in group:
                    true_group = get_cable_group(filename)
                    print(f"  - {filename} (实际组别: {true_group if true_group != -1 else '未知'})")
                print("-" * 40)
            
            # 自动检测并预测未知组别的文件
            unknown_files = [f for f in file_data.keys() if get_cable_group(f) == -1]
            if unknown_files:
                print("\n发现未知组别的文件，开始预测：")
                print("=" * 65)
                for unknown_file in unknown_files:
                    predict_new_file(file_data, model, unknown_file)
            
            print("\n" + "=" * 65)
            print("以上分组是基于模型投票预测的结果")
            
        except Exception as e:
            print(f"程序执行过程中出现错误: {str(e)}") 