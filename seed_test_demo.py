"""
随机种子管理演示脚本
演示如何使用不同的随机种子来获得不同的实验结果
"""

# 导入必要的模块
import sys
import os

# 添加当前目录到路径，以便导入test0805模块
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def run_experiment_with_seed(seed_value):
    """
    使用指定的随机种子运行实验
    
    Args:
        seed_value: 随机种子值
    """
    print(f"\n{'='*80}")
    print(f"实验随机种子: {seed_value}")
    print(f"{'='*80}")
    
    # 动态修改test0805.py中的EXPERIMENT_SEED
    # 读取文件内容
    with open('test0805.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 替换EXPERIMENT_SEED的值
    import re
    pattern = r'EXPERIMENT_SEED = \d+'
    replacement = f'EXPERIMENT_SEED = {seed_value}'
    new_content = re.sub(pattern, replacement, content)
    
    # 写回文件
    with open('test0805.py', 'w', encoding='utf-8') as f:
        f.write(new_content)
    
    print(f"已将随机种子设置为: {seed_value}")
    print("请运行 'python test0805.py' 来查看结果")
    print("或者直接在代码中修改 EXPERIMENT_SEED 变量")

def compare_multiple_seeds():
    """
    比较多个随机种子的结果
    """
    seeds_to_test = [42, 123, 456, 789, 2024]
    
    print("随机种子比较实验")
    print("="*50)
    print("建议测试的随机种子值:")
    
    for i, seed in enumerate(seeds_to_test, 1):
        print(f"{i}. 种子 {seed}")
    
    print("\n使用方法:")
    print("1. 直接修改 test0805.py 中的 EXPERIMENT_SEED 变量")
    print("2. 或者使用本脚本: python seed_test_demo.py")
    print("3. 然后运行: python test0805.py")
    
    print("\n不同随机种子可能产生的影响:")
    print("- 训练集和测试集的划分会不同")
    print("- 随机森林模型的训练结果会不同")
    print("- 最终的准确率、精确率、召回率等指标会有差异")
    print("- 可以通过多次实验来评估模型的稳定性")

if __name__ == "__main__":
    print("随机种子管理工具")
    print("="*50)
    
    # 显示当前的随机种子设置
    try:
        with open('test0805.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        import re
        match = re.search(r'EXPERIMENT_SEED = (\d+)', content)
        if match:
            current_seed = match.group(1)
            print(f"当前随机种子: {current_seed}")
        else:
            print("未找到当前随机种子设置")
    except Exception as e:
        print(f"读取当前种子失败: {e}")
    
    print("\n选择操作:")
    print("1. 修改随机种子")
    print("2. 查看建议的测试种子")
    print("3. 退出")
    
    try:
        choice = input("\n请输入选择 (1-3): ").strip()
        
        if choice == '1':
            seed_input = input("请输入新的随机种子值: ").strip()
            try:
                seed_value = int(seed_input)
                run_experiment_with_seed(seed_value)
            except ValueError:
                print("请输入有效的整数")
        
        elif choice == '2':
            compare_multiple_seeds()
        
        elif choice == '3':
            print("退出程序")
        
        else:
            print("无效选择")
    
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"发生错误: {e}")
