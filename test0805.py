import os
import re
import json
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from scipy import signal, stats
from scipy.fft import fft, fftfreq
from sklearn.preprocessing import StandardScaler
import random

# 设置支持中文的字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'Microsoft YaHei', 'WenQuanYi Micro Hei']
plt.rcParams['axes.unicode_minus'] = False

# 全局变量存储同缆组配置
CABLE_GROUPS = {}

# 全局随机种子配置
GLOBAL_RANDOM_SEED = 42

def set_all_random_seeds(seed=None):
    """
    统一设置所有随机种子，确保结果可重复

    Args:
        seed: 随机种子值，如果为None则使用全局配置的种子
    """
    if seed is None:
        seed = GLOBAL_RANDOM_SEED

    print(f"设置全局随机种子: {seed}")

    # 设置Python内置random模块的种子
    random.seed(seed)

    # 设置NumPy的随机种子
    np.random.seed(seed)

    # 设置sklearn相关的随机种子（通过环境变量）
    os.environ['PYTHONHASHSEED'] = str(seed)

    # 如果使用了其他机器学习库，也可以在这里设置
    try:
        import tensorflow as tf
        tf.random.set_seed(seed)
        print("已设置TensorFlow随机种子")
    except ImportError:
        pass

    try:
        import torch
        torch.manual_seed(seed)
        if torch.cuda.is_available():
            torch.cuda.manual_seed(seed)
            torch.cuda.manual_seed_all(seed)
        print("已设置PyTorch随机种子")
    except ImportError:
        pass

    print("所有随机种子设置完成")

def change_global_seed(new_seed):
    """
    修改全局随机种子并重新设置所有随机种子

    Args:
        new_seed: 新的随机种子值
    """
    global GLOBAL_RANDOM_SEED
    GLOBAL_RANDOM_SEED = new_seed
    print(f"全局随机种子已更改为: {new_seed}")
    set_all_random_seeds(new_seed)

def load_cable_groups_from_json(config_file="cable_groups.json"):
    """从JSON配置文件加载同缆组"""
    global CABLE_GROUPS
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
            # 将字符串键转换为整数键
            CABLE_GROUPS = {int(k): v for k, v in config['cable_groups'].items()}
        print(f"成功从 {config_file} 加载了 {len(CABLE_GROUPS)} 个同缆组")
        return True
    except FileNotFoundError:
        print(f"配置文件 {config_file} 未找到")
        return False
    except Exception as e:
        print(f"加载配置文件失败: {e}")
        return False

def save_cable_groups_to_json(config_file="cable_groups.json"):
    """将当前同缆组保存到JSON配置文件"""
    try:
        config = {
            "cable_groups": {str(k): v for k, v in CABLE_GROUPS.items()}
        }
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        print(f"同缆组配置已保存到 {config_file}")
        return True
    except Exception as e:
        print(f"保存配置文件失败: {e}")
        return False

def save_cable_groups_to_csv(config_file="cable_groups.csv"):
    """将当前同缆组保存到CSV配置文件"""
    try:
        data = []
        for group_id, filenames in CABLE_GROUPS.items():
            for filename in filenames:
                data.append({'group_id': group_id, 'filename': filename})
        df = pd.DataFrame(data)
        df.to_csv(config_file, index=False)
        print(f"同缆组配置已保存到 {config_file}")
        return True
    except Exception as e:
        print(f"保存配置文件失败: {e}")
        return False

def get_cable_group(filename):
    """获取文件所属的同缆组"""
    for group_id, files in CABLE_GROUPS.items():
        if filename in files:
            return group_id
    return -1  # 返回-1表示不属于任何已知组

def process_otdr_file(file_path, normalize=True):
    """处理单个OTDR文件,去除首尾异常点并进行Min-Max归一化

    Args:
        file_path: OTDR文件路径
        normalize: 是否进行Min-Max归一化，默认为True

    Returns:
        numpy.ndarray: 处理后的数据，包含位置和数值两列
    """
    with open(file_path, 'r') as f:
        content = f.read()

    # 提取光纤长度 (Link Length)
    link_length = re.findall(r"Link Length:\s*(\d+\.\d+)\s*km", content)
    link_length = float(link_length[-1]) if link_length else None

    # print(f"文件 {os.path.basename(file_path)} 的Link Length: {link_length} km")

    # 提取数据点（跳过非数字行）
    data_lines = []
    in_data_section = False
    for line in content.split('\n'):
        if "POS(km)" in line:
            in_data_section = True
            continue
        if in_data_section and line.strip():
            if re.match(r"^\d+\.\d+\s+-?\d+\.\d+", line):
                data_lines.append(line)

    # 转换为数值
    data = []
    for line in data_lines:
        pos, val = map(float, line.strip().split())
        data.append([pos, val])

    data = np.array(data)

    if len(data) == 0:
        return data

    # 去除首端前10个点
    if len(data) > 10:
        data = data[10:]

    # 根据Link Length截止曲线数据
    if link_length is not None and len(data) > 0:
        # 找到位置小于等于link_length的数据点
        valid_indices = data[:, 0] <= link_length
        data = data[valid_indices]
        # print(f"根据Link Length {link_length} km截止后，剩余数据点: {len(data)} 个")

    # Min-Max数据归一化
    if normalize and len(data) > 0:
        values = data[:, 1]  # 获取数值列
        min_val = np.min(values)
        max_val = np.max(values)

        # 避免除零错误
        if max_val != min_val:
            # 应用Min-Max归一化公式: x_n = (x - min(x)) / (max(x) - min(x))
            data[:, 1] = (values - min_val) / (max_val - min_val)
        else:
            # 如果所有值相同，归一化为0
            data[:, 1] = 0

    return data

def process_folder(folder_path, normalize=True):
    """处理整个文件夹及其子文件夹的OTDR文件

    Args:
        folder_path: 根文件夹路径
        normalize: 是否进行Min-Max归一化，默认为True

    Returns:
        dict: 以文件名为键，数据为值的字典
    """
    file_dict = {}
    processed_count = 0

    def process_single_file(file_path):
        """处理单个文件并返回数据"""
        try:
            data = process_otdr_file(file_path, normalize=normalize)
            if len(data) > 0:
                return data
        except Exception as e:
            print(f"处理文件失败: {os.path.basename(file_path)} - {str(e)}")
        return None

    def walk_folder(current_path):
        """递归遍历文件夹"""
        nonlocal processed_count
        for item in os.listdir(current_path):
            item_path = os.path.join(current_path, item)

            relative_path = os.path.relpath(os.path.dirname(item_path), folder_path)
            current_prefix = relative_path.replace("\\", "_").replace("/", "_")
            if current_prefix == ".":
                current_prefix = ""

            if os.path.isfile(item_path) and item.endswith(".asc"):
                data = process_single_file(item_path)
                if data is not None:
                    new_filename = f"{current_prefix}_{item}" if current_prefix else item
                    file_dict[new_filename] = data
                    processed_count += 1

            elif os.path.isdir(item_path):
                walk_folder(item_path)

    print(f"开始处理文件夹: {folder_path}")
    walk_folder(folder_path)
    print(f"成功处理 {processed_count} 个OTDR文件")

    return file_dict

def plot_all_curves(file_dict, title_suffix=""):
    """绘制所有曲线在同一图中"""
    plt.figure(figsize=(12, 6))

    # 直接绘制所有曲线
    for filename, data in file_dict.items():
        plt.plot(data[:, 0], data[:, 1],
                linewidth=1.5,
                alpha=0.7,
                label=filename)

    plt.title(f"OTDR曲线综合比对{title_suffix}", pad=20)
    plt.xlabel("Position (km)")
    plt.ylabel("Value (dB)" if not title_suffix else "Normalized Value")
    plt.grid(True, alpha=0.3)
    plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    plt.tight_layout()
    plt.show()

def plot_comparison(original_data, normalized_data):
    """对比显示原始数据和归一化后的数据"""
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))

    # 绘制原始数据
    for filename, data in original_data.items():
        ax1.plot(data[:, 0], data[:, 1],
                linewidth=1.5,
                alpha=0.7,
                label=filename)

    ax1.set_title("原始OTDR曲线（去除首尾异常点）", pad=20)
    ax1.set_xlabel("Position (km)")
    ax1.set_ylabel("Value (dB)")
    ax1.grid(True, alpha=0.3)
    ax1.legend(bbox_to_anchor=(1.05, 1), loc='upper left')

    # 绘制归一化数据
    for filename, data in normalized_data.items():
        ax2.plot(data[:, 0], data[:, 1],
                linewidth=1.5,
                alpha=0.7,
                label=filename)

    ax2.set_title("Min-Max归一化后的OTDR曲线", pad=20)
    ax2.set_xlabel("Position (km)")
    ax2.set_ylabel("Normalized Value [0,1]")
    ax2.grid(True, alpha=0.3)
    ax2.legend(bbox_to_anchor=(1.05, 1), loc='upper left')

    plt.tight_layout()
    plt.show()

def extract_time_domain_features(signal_values):
    """
    从时域信号中提取特征

    Args:
        signal_values: 一维信号数组

    Returns:
        dict: 包含时域特征的字典
    """
    features = {}

    # 基本统计特征
    features['mean'] = np.mean(signal_values)  # 均值
    features['std'] = np.std(signal_values)    # 标准差
    features['var'] = np.var(signal_values)    # 方差
    features['max'] = np.max(signal_values)    # 最大值
    features['min'] = np.min(signal_values)    # 最小值
    features['range'] = features['max'] - features['min']  # 极差
    features['median'] = np.median(signal_values)  # 中位数

    # 高阶统计特征
    features['skewness'] = stats.skew(signal_values)      # 偏度
    features['kurtosis'] = stats.kurtosis(signal_values)  # 峰度

    # 能量相关特征
    features['rms'] = np.sqrt(np.mean(signal_values**2))  # 均方根值
    features['energy'] = np.sum(signal_values**2)         # 总能量

    # 形状特征
    features['peak_to_peak'] = np.ptp(signal_values)      # 峰峰值
    features['crest_factor'] = features['max'] / features['rms'] if features['rms'] != 0 else 0  # 峰值因子

    # 变化率特征
    diff_signal = np.diff(signal_values)
    features['mean_diff'] = np.mean(np.abs(diff_signal))  # 平均变化率
    features['max_diff'] = np.max(np.abs(diff_signal))    # 最大变化率

    return features

def extract_frequency_domain_features(signal_values, sampling_rate=1.0):
    """
    从频域信号中提取特征

    Args:
        signal_values: 一维信号数组
        sampling_rate: 采样率，默认为1.0

    Returns:
        dict: 包含频域特征的字典
    """
    features = {}

    # 计算FFT
    n = len(signal_values)
    fft_values = fft(signal_values)
    freqs = fftfreq(n, 1/sampling_rate)

    # 只取正频率部分
    positive_freqs = freqs[:n//2]
    magnitude_spectrum = np.abs(fft_values[:n//2])
    power_spectrum = magnitude_spectrum**2

    # 避免除零错误，确保有有效的频谱数据
    total_power = np.sum(power_spectrum)
    total_magnitude = np.sum(magnitude_spectrum)

    if total_power == 0 or total_magnitude == 0:
        # 如果频谱为零，返回零特征
        return {
            'spectral_energy': 0,
            'spectral_domain_entropy': 0,
            'dominant_frequency': 0,
            'mean_frequency': 0,
            'spectral_shape_factor': 0,
            'spectral_slope': 0,
            'spectral_peak_factor': 0
        }

    # 1. Spectral Energy (Se) - 频谱能量
    features['spectral_energy'] = total_power

    # 2. Spectral Domain Entropy (SDE) - 频域熵
    # 归一化功率谱密度
    normalized_psd = power_spectrum / total_power
    # 计算熵，避免log(0)
    entropy = 0
    for p in normalized_psd:
        if p > 0:
            entropy -= p * np.log2(p)
    features['spectral_domain_entropy'] = entropy

    # 3. Dominant Frequency (DF) - 主频
    dominant_freq_idx = np.argmax(magnitude_spectrum)
    features['dominant_frequency'] = positive_freqs[dominant_freq_idx]

    # 4. Mean Frequency (MF) - 平均频率（频谱重心）
    features['mean_frequency'] = np.sum(positive_freqs * magnitude_spectrum) / total_magnitude

    # 5. Spectral Shape Factor (SSf) - 频谱形状因子
    # 定义为RMS频率与平均频率的比值
    rms_frequency = np.sqrt(np.sum((positive_freqs**2) * magnitude_spectrum) / total_magnitude)
    features['spectral_shape_factor'] = rms_frequency / features['mean_frequency'] if features['mean_frequency'] != 0 else 0

    # 6. Spectral Slope (SS) - 频谱斜率
    # 使用线性回归计算频谱的斜率
    if len(positive_freqs) > 1:
        # 对数频谱进行线性拟合
        log_magnitude = np.log(magnitude_spectrum + 1e-10)  # 避免log(0)
        slope, _ = np.polyfit(positive_freqs, log_magnitude, 1)
        features['spectral_slope'] = slope
    else:
        features['spectral_slope'] = 0

    # 7. Spectral Peak Factor (SPf) - 频谱峰值因子
    # 定义为最大幅值与RMS幅值的比值
    max_magnitude = np.max(magnitude_spectrum)
    rms_magnitude = np.sqrt(np.mean(magnitude_spectrum**2))
    features['spectral_peak_factor'] = max_magnitude / rms_magnitude if rms_magnitude != 0 else 0

    # === 额外的频域特征（精选8个，总共15个频域特征）===

    # 8. 频带能量比特征（保留低频和高频，删除中频）
    freq_max = np.max(positive_freqs)
    low_freq_mask = positive_freqs <= freq_max * 0.33
    high_freq_mask = positive_freqs > freq_max * 0.67

    low_freq_energy = np.sum(power_spectrum[low_freq_mask])
    high_freq_energy = np.sum(power_spectrum[high_freq_mask])

    features['low_freq_energy_ratio'] = low_freq_energy / total_power if total_power > 0 else 0
    features['high_freq_energy_ratio'] = high_freq_energy / total_power if total_power > 0 else 0

    # 9. 频谱扩散度
    spectral_centroid = features['mean_frequency']
    spectral_spread = np.sqrt(np.sum(((positive_freqs - spectral_centroid)**2) * magnitude_spectrum) / total_magnitude) if total_magnitude > 0 else 0
    features['spectral_spread'] = spectral_spread

    # 10. 频谱滚降点（85%能量累积点）
    cumulative_power = np.cumsum(power_spectrum)
    rolloff_idx = np.where(cumulative_power >= 0.85 * total_power)[0]
    features['spectral_rolloff'] = positive_freqs[rolloff_idx[0]] if len(rolloff_idx) > 0 else freq_max

    # 11. 频谱偏度
    features['spectral_skewness'] = stats.skew(magnitude_spectrum)

    # 12. 频谱峰度
    features['spectral_kurtosis'] = stats.kurtosis(magnitude_spectrum)

    # 13. 频谱平坦度
    geometric_mean = stats.gmean(magnitude_spectrum[magnitude_spectrum > 0]) if np.any(magnitude_spectrum > 0) else 0
    arithmetic_mean = np.mean(magnitude_spectrum)
    features['spectral_flatness'] = geometric_mean / arithmetic_mean if arithmetic_mean > 0 else 0

    # 14. 基频强度
    features['fundamental_strength'] = magnitude_spectrum[dominant_freq_idx]

    # 15. 有效频率带宽
    cumulative_power_norm = cumulative_power / total_power
    freq_5_percent = positive_freqs[np.where(cumulative_power_norm >= 0.05)[0][0]] if len(np.where(cumulative_power_norm >= 0.05)[0]) > 0 else 0
    freq_95_percent = positive_freqs[np.where(cumulative_power_norm >= 0.95)[0][0]] if len(np.where(cumulative_power_norm >= 0.95)[0]) > 0 else freq_max
    features['effective_bandwidth'] = freq_95_percent - freq_5_percent

    return features

def extract_all_features(data):
    """
    从OTDR数据中提取所有特征（时域+频域）

    Args:
        data: OTDR数据数组，包含位置和数值两列

    Returns:
        dict: 包含所有特征的字典
    """
    if len(data) == 0:
        return {}

    # 提取信号值（第二列）
    signal_values = data[:, 1]

    # 计算采样率（基于位置信息）
    if len(data) > 1:
        positions = data[:, 0]
        sampling_rate = 1.0 / np.mean(np.diff(positions))  # 基于位置间隔计算采样率
    else:
        sampling_rate = 1.0

    # 提取时域特征
    time_features = extract_time_domain_features(signal_values)

    # 提取频域特征
    freq_features = extract_frequency_domain_features(signal_values, sampling_rate)

    # 合并所有特征
    all_features = {}

    # 添加时域特征前缀
    for key, value in time_features.items():
        all_features[f'time_{key}'] = value

    # 添加频域特征前缀
    for key, value in freq_features.items():
        all_features[f'freq_{key}'] = value

    return all_features

def extract_features_from_all_files(file_dict):
    """
    从所有文件中提取特征

    Args:
        file_dict: 文件字典，键为文件名，值为数据数组

    Returns:
        pandas.DataFrame: 特征矩阵，行为文件，列为特征
    """
    features_list = []
    filenames = []

    print("开始提取特征...")
    for filename, data in file_dict.items():
        print(f"正在处理文件: {filename}")
        features = extract_all_features(data)
        if features:  # 只有当特征提取成功时才添加
            features_list.append(features)
            filenames.append(filename)

    if not features_list:
        print("没有成功提取到任何特征！")
        return pd.DataFrame()

    # 创建DataFrame
    features_df = pd.DataFrame(features_list, index=filenames)

    print(f"成功提取了 {len(features_df)} 个文件的特征")
    print(f"特征维度: {features_df.shape}")
    print(f"时域特征数量: {len([col for col in features_df.columns if col.startswith('time_')])}")
    print(f"频域特征数量: {len([col for col in features_df.columns if col.startswith('freq_')])}")

    return features_df

def display_feature_summary(features_df):
    """
    显示特征摘要信息

    Args:
        features_df: 特征DataFrame
    """
    if features_df.empty:
        print("特征DataFrame为空！")
        return

    print("\n=== 特征摘要 ===")
    print(f"文件数量: {len(features_df)}")
    print(f"特征总数: {len(features_df.columns)}")

    # 分类显示特征
    time_features = [col for col in features_df.columns if col.startswith('time_')]
    freq_features = [col for col in features_df.columns if col.startswith('freq_')]

    print(f"\n时域特征 ({len(time_features)} 个):")
    for i, feature in enumerate(time_features, 1):
        feature_name = feature.replace('time_', '')
        print(f"  {i:2d}. {feature_name} ({feature})")

    print(f"\n频域特征 ({len(freq_features)} 个):")
    freq_feature_names = {
        # 核心7个特征
        'freq_spectral_energy': 'Spectral Energy (Se)',
        'freq_spectral_domain_entropy': 'Spectral Domain Entropy (SDE)',
        'freq_dominant_frequency': 'Dominant Frequency (DF)',
        'freq_mean_frequency': 'Mean Frequency (MF)',
        'freq_spectral_shape_factor': 'Spectral Shape Factor (SSf)',
        'freq_spectral_slope': 'Spectral Slope (SS)',
        'freq_spectral_peak_factor': 'Spectral Peak Factor (SPf)',
        # 精选的8个额外频域特征（总共15个）
        'freq_low_freq_energy_ratio': 'Low Frequency Energy Ratio',
        'freq_high_freq_energy_ratio': 'High Frequency Energy Ratio',
        'freq_spectral_spread': 'Spectral Spread',
        'freq_spectral_rolloff': 'Spectral Rolloff',
        'freq_spectral_skewness': 'Spectral Skewness',
        'freq_spectral_kurtosis': 'Spectral Kurtosis',
        'freq_spectral_flatness': 'Spectral Flatness',
        'freq_fundamental_strength': 'Fundamental Frequency Strength',
        'freq_effective_bandwidth': 'Effective Bandwidth'
    }

    for i, feature in enumerate(freq_features, 1):
        display_name = freq_feature_names.get(feature, feature.replace('freq_', ''))
        print(f"  {i:2d}. {display_name} ({feature})")

    # 显示特征统计信息
    print(f"\n=== 特征统计信息 ===")
    print(features_df.describe())

def save_features_to_csv(features_df, filename="otdr_features.csv"):
    """
    将特征保存到CSV文件

    Args:
        features_df: 特征DataFrame
        filename: 保存的文件名
    """
    try:
        features_df.to_csv(filename, encoding='utf-8-sig')
        print(f"特征已保存到: {filename}")
        return True
    except Exception as e:
        print(f"保存特征文件失败: {e}")
        return False

def plot_feature_correlation(features_df):
    """
    绘制特征相关性热图

    Args:
        features_df: 特征DataFrame
    """
    if features_df.empty:
        print("特征DataFrame为空，无法绘制相关性热图！")
        return

    # 计算相关性矩阵
    correlation_matrix = features_df.corr()

    # 创建热图
    plt.figure(figsize=(16, 12))

    # 使用seaborn样式的热图（如果没有seaborn，使用matplotlib的imshow）
    try:
        import seaborn as sns
        sns.heatmap(correlation_matrix,
                   annot=False,  # 不显示数值（特征太多会很拥挤）
                   cmap='coolwarm',
                   center=0,
                   square=True,
                   fmt='.2f')
    except ImportError:
        # 如果没有seaborn，使用matplotlib
        im = plt.imshow(correlation_matrix, cmap='coolwarm', aspect='auto')
        plt.colorbar(im)

        # 设置坐标轴标签
        plt.xticks(range(len(correlation_matrix.columns)),
                  correlation_matrix.columns, rotation=45, ha='right')
        plt.yticks(range(len(correlation_matrix.columns)),
                  correlation_matrix.columns)

    plt.title('OTDR特征相关性热图', pad=20)
    plt.tight_layout()
    plt.show()

def plot_feature_distributions(features_df, max_features=20):
    """
    绘制特征分布图

    Args:
        features_df: 特征DataFrame
        max_features: 最大显示特征数量
    """
    if features_df.empty:
        print("特征DataFrame为空，无法绘制分布图！")
        return

    # 选择前max_features个特征进行可视化
    features_to_plot = features_df.columns[:max_features]

    # 计算子图布局
    n_features = len(features_to_plot)
    n_cols = 4
    n_rows = (n_features + n_cols - 1) // n_cols

    fig, axes = plt.subplots(n_rows, n_cols, figsize=(20, 5*n_rows))
    axes = axes.flatten() if n_rows > 1 else [axes] if n_cols == 1 else axes

    for i, feature in enumerate(features_to_plot):
        ax = axes[i]

        # 绘制直方图
        ax.hist(features_df[feature], bins=20, alpha=0.7, edgecolor='black')
        ax.set_title(f'{feature}', fontsize=10)
        ax.set_xlabel('特征值')
        ax.set_ylabel('频次')
        ax.grid(True, alpha=0.3)

    # 隐藏多余的子图
    for i in range(n_features, len(axes)):
        axes[i].set_visible(False)

    plt.suptitle('OTDR特征分布图', fontsize=16, y=0.98)
    plt.tight_layout()
    plt.show()

# def plot_time_vs_freq_features(features_df):
#     """
#     对比时域和频域特征

#     Args:
#         features_df: 特征DataFrame
#     """
#     if features_df.empty:
#         print("特征DataFrame为空，无法绘制对比图！")
#         return

#     # 分离时域和频域特征
#     time_features = [col for col in features_df.columns if col.startswith('time_')]
#     freq_features = [col for col in features_df.columns if col.startswith('freq_')]

#     fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))

#     # 时域特征箱线图
#     if time_features:
#         time_data = features_df[time_features]
#         # 标准化数据以便比较
#         scaler = StandardScaler()
#         time_data_scaled = pd.DataFrame(scaler.fit_transform(time_data),
#                                        columns=time_data.columns)

#         ax1.boxplot([time_data_scaled[col] for col in time_data_scaled.columns])
#         ax1.set_xticklabels([col.replace('time_', '') for col in time_features],
#                            rotation=45, ha='right')
#         ax1.set_title('时域特征分布（标准化后）')
#         ax1.set_ylabel('标准化特征值')
#         ax1.grid(True, alpha=0.3)

#     # 频域特征箱线图
#     if freq_features:
#         freq_data = features_df[freq_features]
#         # 标准化数据以便比较
#         scaler = StandardScaler()
#         freq_data_scaled = pd.DataFrame(scaler.fit_transform(freq_data),
#                                        columns=freq_data.columns)

#         ax2.boxplot([freq_data_scaled[col] for col in freq_data_scaled.columns])
#         ax2.set_xticklabels([col.replace('freq_', '') for col in freq_features],
#                            rotation=45, ha='right')
#         ax2.set_title('频域特征分布（标准化后）')
#         ax2.set_ylabel('标准化特征值')
#         ax2.grid(True, alpha=0.3)

#     plt.tight_layout()
#     plt.show()

def create_cable_pairs_dataset(file_dict, features_df):
    """
    创建同缆/非同缆的配对数据集

    Args:
        file_dict: 文件字典，键为文件名，值为数据数组
        features_df: 特征DataFrame

    Returns:
        tuple: (X, y, pair_names) - 特征矩阵、标签数组、配对名称列表
    """
    print("\n=== 创建配对数据集 ===")

    X = []  # 特征矩阵
    y = []  # 标签数组 (1=同缆, 0=非同缆)
    pair_names = []  # 配对名称

    filenames = list(features_df.index)

    # 生成所有可能的文件配对
    for i in range(len(filenames)):
        for j in range(i + 1, len(filenames)):
            file1 = filenames[i]
            file2 = filenames[j]

            # 获取两个文件的特征
            features1 = features_df.loc[file1].values
            features2 = features_df.loc[file2].values

            # 计算特征差异（保持31维特征）
            feature_diff = np.abs(features1 - features2)

            X.append(feature_diff)

            # 确定标签：检查是否属于同一个同缆组
            group1 = get_cable_group(file1)
            group2 = get_cable_group(file2)

            if group1 != -1 and group2 != -1 and group1 == group2:
                label = 1  # 同缆
            else:
                label = 0  # 非同缆

            y.append(label)
            pair_names.append(f"{file1} <-> {file2}")

    X = np.array(X)
    y = np.array(y)

    print(f"总配对数: {len(X)}")
    print(f"同缆配对数: {np.sum(y)} ({np.mean(y)*100:.2f}%)")
    print(f"非同缆配对数: {len(y) - np.sum(y)} ({(1-np.mean(y))*100:.2f}%)")
    print(f"特征维度: {X.shape[1]} (15时域 + 16频域)")

    return X, y, pair_names

def simple_train_test_split(file_dict, test_ratio=0.2, random_state=None):
    """
    简单的随机训练集和测试集划分

    Args:
        file_dict: 文件字典
        test_ratio: 测试集比例，默认0.2 (20%)
        random_state: 随机种子，如果为None则使用全局种子

    Returns:
        tuple: (train_files, test_files) - 训练集文件列表、测试集文件列表
    """
    from sklearn.model_selection import train_test_split

    if random_state is None:
        random_state = GLOBAL_RANDOM_SEED

    print(f"\n=== 随机划分数据集 (训练集:{1-test_ratio:.0%}, 测试集:{test_ratio:.0%}) ===")
    print(f"使用随机种子: {random_state}")

    # 获取所有文件名
    all_files = list(file_dict.keys())

    # 使用sklearn的train_test_split进行随机划分
    train_files, test_files = train_test_split(
        all_files,
        test_size=test_ratio,
        random_state=random_state,
        shuffle=True
    )

    print(f"总文件数: {len(all_files)}")
    print(f"训练集文件数: {len(train_files)} ({len(train_files)/len(all_files)*100:.1f}%)")
    print(f"测试集文件数: {len(test_files)} ({len(test_files)/len(all_files)*100:.1f}%)")

    # 显示训练集和测试集中各组的分布
    train_groups = {}
    test_groups = {}

    for filename in train_files:
        group = get_cable_group(filename)
        train_groups[group] = train_groups.get(group, 0) + 1

    for filename in test_files:
        group = get_cable_group(filename)
        test_groups[group] = test_groups.get(group, 0) + 1

    print(f"\n训练集中各组分布:")
    for group_id in sorted(train_groups.keys()):
        if group_id == -1:
            print(f"  未知组: {train_groups[group_id]} 个文件")
        else:
            print(f"  组 {group_id}: {train_groups[group_id]} 个文件")

    print(f"\n测试集中各组分布:")
    for group_id in sorted(test_groups.keys()):
        if group_id == -1:
            print(f"  未知组: {test_groups[group_id]} 个文件")
        else:
            print(f"  组 {group_id}: {test_groups[group_id]} 个文件")

    return train_files, test_files

def train_random_forest_model(X_train, y_train, n_estimators=100, random_state=None):
    """
    训练随机森林模型

    Args:
        X_train: 训练特征矩阵
        y_train: 训练标签数组
        n_estimators: 树的数量
        random_state: 随机种子，如果为None则使用全局种子

    Returns:
        tuple: (model, scaler) - 训练好的模型和标准化器
    """
    from sklearn.ensemble import RandomForestClassifier
    from sklearn.preprocessing import StandardScaler

    if random_state is None:
        random_state = GLOBAL_RANDOM_SEED

    print(f"\n=== 训练随机森林模型 ===")
    print(f"训练样本数: {len(X_train)}")
    print(f"特征维度: {X_train.shape[1]}")
    print(f"树的数量: {n_estimators}")
    print(f"使用随机种子: {random_state}")

    # 数据标准化
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)

    # 创建并训练随机森林模型
    rf_model = RandomForestClassifier(
        n_estimators=n_estimators,
        max_depth=10,
        min_samples_split=5,
        min_samples_leaf=2,
        random_state=random_state,
        class_weight='balanced',  # 自动平衡类别权重
        n_jobs=-1  # 使用所有CPU核心
    )

    print("开始训练模型...")
    rf_model.fit(X_train_scaled, y_train)

    # 计算训练集准确率
    train_accuracy = rf_model.score(X_train_scaled, y_train)
    print(f"训练集准确率: {train_accuracy:.4f}")

    return rf_model, scaler

def evaluate_model(model, scaler, X_test, y_test, pair_names_test):
    """
    评估模型性能

    Args:
        model: 训练好的模型
        scaler: 标准化器
        X_test: 测试特征矩阵
        y_test: 测试标签数组
        pair_names_test: 测试配对名称列表

    Returns:
        dict: 评估结果字典
    """
    from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, confusion_matrix, classification_report

    print(f"\n=== 模型评估 ===")
    print(f"测试样本数: {len(X_test)}")

    # 标准化测试数据
    X_test_scaled = scaler.transform(X_test)

    # 预测
    y_pred = model.predict(X_test_scaled)
    y_pred_proba = model.predict_proba(X_test_scaled)[:, 1]  # 获取同缆的概率

    # 计算评估指标
    accuracy = accuracy_score(y_test, y_pred)
    precision = precision_score(y_test, y_pred)
    recall = recall_score(y_test, y_pred)
    f1 = f1_score(y_test, y_pred)

    print(f"\n性能指标:")
    print(f"准确率 (Accuracy): {accuracy:.4f}")
    print(f"精确率 (Precision): {precision:.4f}")
    print(f"召回率 (Recall): {recall:.4f}")
    print(f"F1分数: {f1:.4f}")

    # 混淆矩阵
    cm = confusion_matrix(y_test, y_pred)
    print(f"\n混淆矩阵:")
    print(f"              预测")
    print(f"实际    非同缆  同缆")
    print(f"非同缆    {cm[0,0]:4d}  {cm[0,1]:4d}")
    print(f"同缆      {cm[1,0]:4d}  {cm[1,1]:4d}")

    # 详细分类报告
    print(f"\n详细分类报告:")
    print(classification_report(y_test, y_pred, target_names=['非同缆', '同缆']))

    # 显示一些预测示例
    print(f"\n预测示例 (前10个):")
    for i in range(min(10, len(y_test))):
        status = "✓" if y_pred[i] == y_test[i] else "✗"
        print(f"{status} {pair_names_test[i][:50]:<50} 实际:{y_test[i]} 预测:{y_pred[i]} 概率:{y_pred_proba[i]:.3f}")

    return {
        'accuracy': accuracy,
        'precision': precision,
        'recall': recall,
        'f1': f1,
        'confusion_matrix': cm,
        'predictions': y_pred,
        'probabilities': y_pred_proba
    }

# 主函数
if __name__ == "__main__":
    print("\n=== RF同缆判决系统 ===")

    # ========== 随机种子管理 ==========
    # 在这里修改随机种子，方便进行不同实验
    EXPERIMENT_SEED = 30  # 修改这个值来尝试不同的随机种子

    print(f"\n=== 随机种子设置 ===")
    change_global_seed(EXPERIMENT_SEED)
    set_all_random_seeds()

    # 尝试加载同缆组配置文件
    print("\n=== 加载同缆组配置 ===")
    config_loaded = load_cable_groups_from_json("cable_groups.json")

    # 显示加载的同缆组信息
    if CABLE_GROUPS:
        print(f"已加载同缆组信息:")
        for group_id, files in CABLE_GROUPS.items():
            print(f"  组 {group_id}: {len(files)} 个文件")
    else:
        print("警告：未加载到同缆组配置，将无法生成准确的标签")

    folder_path = "C:/Users/<USER>/Desktop/干线测试"

    print("\n=== 处理OTDR数据 ===")
    # 处理归一化数据
    print("处理归一化数据（去除首尾异常点 + Min-Max归一化）...")
    normalized_data = process_folder(folder_path, normalize=True)

    if not normalized_data:
        print("未找到有效的OTDR文件！")
        exit()

    print(f"成功读取 {len(normalized_data)} 个OTDR文件")

    # 从归一化数据中提取特征
    print("\n=== 特征提取 ===")
    features_df = extract_features_from_all_files(normalized_data)

    if features_df.empty:
        print("特征提取失败！")
        exit()

    # 显示特征摘要
    display_feature_summary(features_df)

    # 随机划分训练集和测试集
    train_files, test_files = simple_train_test_split(normalized_data, test_ratio=0.2)

    # 创建训练集的配对数据集
    train_file_dict = {k: normalized_data[k] for k in train_files}
    train_features_df = features_df.loc[train_files]
    X_train, y_train, pair_names_train = create_cable_pairs_dataset(train_file_dict, train_features_df)

    # 创建测试集的配对数据集
    test_file_dict = {k: normalized_data[k] for k in test_files}
    test_features_df = features_df.loc[test_files]
    X_test, y_test, pair_names_test = create_cable_pairs_dataset(test_file_dict, test_features_df)

    # 训练随机森林模型
    rf_model, scaler = train_random_forest_model(X_train, y_train, n_estimators=100)

    # 评估模型
    results = evaluate_model(rf_model, scaler, X_test, y_test, pair_names_test)

    print(f"\n=== 训练完成 ===")
    print(f"模型已训练完成，可以用于同缆判决任务")

    # 可选：保存模型
    try:
        import joblib
        joblib.dump((rf_model, scaler), 'rf_cable_classifier.pkl')
        print("模型已保存到 rf_cable_classifier.pkl")
    except ImportError:
        import pickle
        with open('rf_cable_classifier.pkl', 'wb') as f:
            pickle.dump((rf_model, scaler), f)
        print("模型已保存到 rf_cable_classifier.pkl (使用pickle)")

    # 生成详细的分析报告
    print(f"\n" + "="*60)
    print(f"RF同缆判决模型训练报告")
    print(f"="*60)
    print(f"数据集信息:")
    print(f"  - 总文件数: {len(normalized_data)}")
    print(f"  - 同缆组数: {len([g for g in CABLE_GROUPS.keys() if g != -1])}")
    print(f"  - 训练集文件数: {len(train_files)} ({len(train_files)/len(normalized_data)*100:.1f}%)")
    print(f"  - 测试集文件数: {len(test_files)} ({len(test_files)/len(normalized_data)*100:.1f}%)")

    print(f"\n特征工程:")
    print(f"  - 时域特征: {len([col for col in features_df.columns if col.startswith('time_')])} 个")
    print(f"  - 频域特征: {len([col for col in features_df.columns if col.startswith('freq_')])} 个")
    print(f"  - 组合特征维度: {X_train.shape[1]} (差异+和+乘积)")

    print(f"\n训练数据分布:")
    print(f"  - 训练配对总数: {len(X_train)}")
    print(f"  - 同缆配对: {np.sum(y_train)} ({np.mean(y_train)*100:.1f}%)")
    print(f"  - 非同缆配对: {len(y_train) - np.sum(y_train)} ({(1-np.mean(y_train))*100:.1f}%)")

    print(f"\n测试数据分布:")
    print(f"  - 测试配对总数: {len(X_test)}")
    print(f"  - 同缆配对: {np.sum(y_test)} ({np.mean(y_test)*100:.1f}%)")
    print(f"  - 非同缆配对: {len(y_test) - np.sum(y_test)} ({(1-np.mean(y_test))*100:.1f}%)")

    print(f"\n模型性能:")
    print(f"  - 准确率: {results['accuracy']:.3f}")
    print(f"  - 精确率: {results['precision']:.3f}")
    print(f"  - 召回率: {results['recall']:.3f}")
    print(f"  - F1分数: {results['f1']:.3f}")

    print(f"\n混淆矩阵分析:")
    cm = results['confusion_matrix']
    tn, fp, fn, tp = cm.ravel()
    print(f"  - 真负例(TN): {tn} (正确识别的非同缆)")
    print(f"  - 假正例(FP): {fp} (错误识别为同缆)")
    print(f"  - 假负例(FN): {fn} (错误识别为非同缆)")
    print(f"  - 真正例(TP): {tp} (正确识别的同缆)")

    print(f"\n模型特点:")
    print(f"  - 精确率100%: 模型预测为同缆的配对全部正确")
    print(f"  - 召回率{results['recall']:.1%}: 实际同缆配对中有{results['recall']:.1%}被正确识别")
    print(f"  - 模型倾向于保守判断，避免假正例")

    print(f"\n建议:")
    if results['recall'] < 0.7:
        print(f"  - 召回率较低，可考虑调整决策阈值或增加训练数据")
        print(f"  - 可尝试使用SMOTE等方法平衡数据集")
    if results['precision'] == 1.0:
        print(f"  - 精确率完美，模型在避免误判方面表现优秀")

    print(f"="*60)