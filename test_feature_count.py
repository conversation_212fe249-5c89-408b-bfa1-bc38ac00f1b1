import numpy as np
from test730 import extract_all_features

# 创建简单的测试数据
positions = np.arange(0, 5, 0.01)
signal_values = np.random.randn(len(positions))
signal_values = (signal_values - np.min(signal_values)) / (np.max(signal_values) - np.min(signal_values))
test_data = np.column_stack([positions, signal_values])

# 提取特征
features = extract_all_features(test_data)

# 统计特征数量
time_features = [k for k in features.keys() if k.startswith('time_')]
freq_features = [k for k in features.keys() if k.startswith('freq_')]

print(f"总特征数量: {len(features)}")
print(f"时域特征数量: {len(time_features)}")
print(f"频域特征数量: {len(freq_features)}")

print(f"\n时域特征:")
for i, feature in enumerate(time_features, 1):
    print(f"  {i:2d}. {feature}")

print(f"\n频域特征:")
for i, feature in enumerate(freq_features, 1):
    print(f"  {i:2d}. {feature}")
