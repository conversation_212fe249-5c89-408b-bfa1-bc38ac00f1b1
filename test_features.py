import numpy as np
import pandas as pd
from test730 import extract_all_features, display_feature_summary

# 创建模拟的OTDR数据进行测试
def create_test_data():
    """创建测试用的OTDR数据"""
    # 模拟位置数据（0到10km，间隔0.01km）
    positions = np.arange(0, 10, 0.01)
    
    # 模拟信号数据（包含趋势、噪声和一些特征）
    # 基础衰减趋势
    base_signal = -0.2 * positions - 20
    
    # 添加一些反射峰
    reflection1 = -2 * np.exp(-((positions - 2.5)**2) / 0.01)
    reflection2 = -1.5 * np.exp(-((positions - 5.0)**2) / 0.02)
    reflection3 = -1.0 * np.exp(-((positions - 7.5)**2) / 0.015)
    
    # 添加噪声
    noise = 0.1 * np.random.randn(len(positions))
    
    # 合成信号
    signal_values = base_signal + reflection1 + reflection2 + reflection3 + noise
    
    # 归一化到[0,1]
    signal_values = (signal_values - np.min(signal_values)) / (np.max(signal_values) - np.min(signal_values))
    
    # 组合位置和信号数据
    data = np.column_stack([positions, signal_values])
    
    return data

def test_feature_extraction():
    """测试特征提取功能"""
    print("=== 特征提取测试 ===")
    
    # 创建测试数据
    print("创建测试数据...")
    test_data = create_test_data()
    print(f"测试数据形状: {test_data.shape}")
    
    # 提取特征
    print("\n提取特征...")
    features = extract_all_features(test_data)
    
    if features:
        print(f"成功提取 {len(features)} 个特征")
        
        # 分类显示特征
        time_features = {k: v for k, v in features.items() if k.startswith('time_')}
        freq_features = {k: v for k, v in features.items() if k.startswith('freq_')}
        
        print(f"\n时域特征 ({len(time_features)} 个):")
        for i, (key, value) in enumerate(time_features.items(), 1):
            print(f"  {i:2d}. {key}: {value:.6f}")
        
        print(f"\n频域特征 ({len(freq_features)} 个):")
        for i, (key, value) in enumerate(freq_features.items(), 1):
            print(f"  {i:2d}. {key}: {value:.6f}")
        
        # 创建DataFrame进行进一步分析
        features_df = pd.DataFrame([features], index=['test_signal'])
        
        print(f"\n=== 特征DataFrame信息 ===")
        print(f"形状: {features_df.shape}")
        print(f"列名: {list(features_df.columns)}")
        
        # 保存测试结果
        features_df.to_csv("test_features_output.csv", encoding='utf-8-sig')
        print(f"\n测试特征已保存到: test_features_output.csv")
        
    else:
        print("特征提取失败！")

if __name__ == "__main__":
    test_feature_extraction()
